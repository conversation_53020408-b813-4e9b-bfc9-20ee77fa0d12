import type React from 'react';
import {
  SettingOutlined,
  MoreOutlined,
  CopyOutlined,
  EditOutlined,
  DeleteOutlined,
  Bar<PERSON>hartOutlined,
  TableOutlined,
  ExpandOutlined,
} from '@ant-design/icons';
import { useState, useEffect } from 'react';
import { Table, Dropdown, Menu, Modal, Input, message, Tooltip } from 'antd';
import FullScreenChartModal from '../FullScreenChartModal/index';
import { editChart, deleteChart } from '@/services/DataLoom/yibiaopanjiekou';
import { extractTableFromOption } from '@/utils/echartsToTable';

interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  showFullScreenLink?: boolean;
  showAnalyze?: boolean; // 添加控制智能分析按钮显示的属性
  data?: any[]; // 表格数据
  columns?: any[]; // 表格列定义
  chartId: string; // 添加chartId属性
  dashboardId: string; // 添加dashboardId属性
  chartName: string; // 添加chartName属性
  onRename?: (newTitle: string) => void;
  onDelete?: () => void;
  onAnalyze?: () => void; // 添加智能分析回调
  chartOption?: any; // 添加图表配置选项
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  children,
  showFullScreenLink = true,
  showAnalyze = true, // 设置默认值为 true
  data = [],
  columns = [],
  chartId,
  dashboardId,
  chartName,
  onRename,
  onDelete,
  onAnalyze,
  chartOption,
}) => {
  const [showChart, setShowChart] = useState(true);
  const [isFullScreenVisible, setIsFullScreenVisible] = useState(false);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState(title);
  const [tableData, setTableData] = useState<{ columns: any[]; dataSource: any[] }>({ columns: [], dataSource: [] });
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (chartOption) {
      const { columns: extractedColumns, dataSource } = extractTableFromOption(chartOption);
      setTableData({
        columns: extractedColumns,
        dataSource: dataSource,
      });
    }
  }, [chartOption]);

  const showFullScreen = () => {
    setIsFullScreenVisible(true);
  };

  const hideFullScreen = () => {
    setIsFullScreenVisible(false);
  };

  const toggleChart = () => {
    setShowChart(!showChart);
  };

  const handleRename = async () => {
    try {
      const params = {
        dashboardId: dashboardId,
        chartName: newTitle,
        id: chartId,
      };
      const res = await editChart(params);
      if (res.code === 0) {
        if (onRename) {
          onRename(newTitle);
        }
        message.success('重命名成功');
        setIsRenameModalVisible(false);
      } else {
        message.error(res.message || '重命名失败');
      }
    } catch (error) {
      message.error('重命名失败');
    }
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个图表吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const params = {
            chartId: chartId,
          };
          await deleteChart(params);
          if (onDelete) {
            onDelete();
          }
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleCopy = () => {
    // 复制图表功能
    message.info('复制图表功能开发中...');
  };

  const handleCopyTo = () => {
    // 复制到其他仪表盘功能
    message.info('复制到其他仪表盘功能开发中...');
  };

  const handleConfig = () => {
    // 配置功能
    message.info('配置功能开发中...');
  };

  // 悬浮菜单项配置
  const hoverMenuItems = [
    {
      key: 'analyze',
      icon: <BarChartOutlined />,
      label: '智能分析',
      onClick: onAnalyze,
      show: showAnalyze,
    },
    {
      key: 'config',
      icon: <SettingOutlined />,
      label: '配置',
      onClick: handleConfig,
      show: true,
    },
    {
      key: 'table',
      icon: <TableOutlined />,
      label: '查看表格',
      onClick: toggleChart,
      show: true,
    },
    {
      key: 'fullscreen',
      icon: <ExpandOutlined />,
      label: '查看大图',
      onClick: showFullScreen,
      show: showFullScreenLink,
    },
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: '复制',
      onClick: handleCopy,
      show: true,
    },
    {
      key: 'copyTo',
      icon: <CopyOutlined />,
      label: '复制到',
      onClick: handleCopyTo,
      show: true,
    },
    {
      key: 'rename',
      icon: <EditOutlined />,
      label: '重命名',
      onClick: () => setIsRenameModalVisible(true),
      show: true,
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      onClick: handleDelete,
      show: true,
      danger: true,
    },
  ];

  return (
    <div className="chart-container" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <div className="chart-header">
        <Tooltip title={title}>
          <span className="chart-title">{title}</span>
        </Tooltip>
        <div className="chart-actions">
          {showAnalyze && (
            <span className="action-link" onClick={onAnalyze}>
              智能分析
            </span>
          )}
          {/* 悬浮菜单 */}
          {isHovered && (
            <div className="hover-menu">
              <Dropdown
                menu={{
                  items: hoverMenuItems
                    .filter((item) => item.show)
                    .map((item) => ({
                      key: item.key,
                      icon: item.icon,
                      label: item.label,
                      onClick: item.onClick,
                      danger: item.danger,
                    })),
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <div className="hover-menu-trigger">
                  <MoreOutlined />
                </div>
              </Dropdown>
            </div>
          )}
        </div>
      </div>
      <div className="chart-content">
        {showChart ? (
          children
        ) : (
          <Table
            dataSource={tableData.dataSource}
            columns={tableData.columns}
            pagination={false}
            size="small"
            scroll={{ y: 200 }}
            bordered
          />
        )}
      </div>

      <FullScreenChartModal
        visible={isFullScreenVisible}
        onClose={hideFullScreen}
        title={title}
        width="80%"
        height="85vh"
        showSettings={true}
        initialMode={showChart ? 'chart' : 'table'}
        chartOption={chartOption}
        onRename={() => setIsRenameModalVisible(true)}
        onDelete={() => handleDelete()}
      >
        {children}
      </FullScreenChartModal>

      <Modal
        title="重命名图表"
        className="rename-chart-modal common-modal"
        zIndex={9999} //最大层级
        open={isRenameModalVisible}
        onOk={handleRename}
        onCancel={() => setIsRenameModalVisible(false)}
      >
        <Input value={newTitle} onChange={(e) => setNewTitle(e.target.value)} placeholder="请输入新的图表名称" />
      </Modal>
    </div>
  );
};

export default ChartContainer;
